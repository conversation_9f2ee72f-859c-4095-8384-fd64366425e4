"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/ToolNode.tsx":
/*!********************************************************!*\
  !*** ./src/components/manual-build/nodes/ToolNode.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ToolNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nconst toolIcons = {\n    google_drive: '📁',\n    google_docs: '📄',\n    zapier: '⚡',\n    notion: '📝',\n    google_sheets: '📊',\n    calendar: '📅',\n    gmail: '📧',\n    youtube: '📺',\n    supabase: '🗄️'\n};\nconst toolNames = {\n    google_drive: 'Google Drive',\n    google_docs: 'Google Docs',\n    zapier: 'Zapier',\n    notion: 'Notion',\n    google_sheets: 'Google Sheets',\n    calendar: 'Calendar',\n    gmail: 'Gmail',\n    youtube: 'YouTube',\n    supabase: 'Supabase'\n};\nfunction ToolNode(param) {\n    let { data } = param;\n    const config = data.config;\n    const toolType = config === null || config === void 0 ? void 0 : config.toolType;\n    const toolIcon = toolType ? toolIcons[toolType] : '🔧';\n    const toolName = toolType ? toolNames[toolType] : 'External Tool';\n    const connectionStatus = (config === null || config === void 0 ? void 0 : config.connectionStatus) || 'disconnected';\n    const getStatusColor = ()=>{\n        switch(connectionStatus){\n            case 'connected':\n                return 'text-green-400';\n            case 'error':\n                return 'text-red-400';\n            default:\n                return 'text-yellow-400';\n        }\n    };\n    const getStatusText = ()=>{\n        switch(connectionStatus){\n            case 'connected':\n                return 'Connected';\n            case 'error':\n                return 'Error';\n            default:\n                return 'Not Connected';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        color: \"#06b6d4\",\n        hasInput: true,\n        hasOutput: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: toolType ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: toolIcon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                        lineNumber: 74,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-white\",\n                                        children: toolName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                        lineNumber: 75,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs \".concat(getStatusColor()),\n                                children: \"●\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs \".concat(getStatusColor()),\n                        children: getStatusText()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 13\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.timeout) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: [\n                            \"Timeout: \",\n                            config.timeout,\n                            \"s\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 15\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-cyan-300 bg-cyan-900/20 px-2 py-1 rounded\",\n                        children: \"✓ Tool configured\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 96,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                lineNumber: 71,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-300\",\n                        children: \"External Tool Integration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 102,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: \"Connect to external services like Google Drive, databases, APIs, or browser automation.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: \"⚠️ Needs configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                lineNumber: 101,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n            lineNumber: 69,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n        lineNumber: 62,\n        columnNumber: 5\n    }, this);\n}\n_c = ToolNode;\nvar _c;\n$RefreshReg$(_c, \"ToolNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/ToolNode.tsx\n"));

/***/ })

});