"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/ToolNode.tsx":
/*!********************************************************!*\
  !*** ./src/components/manual-build/nodes/ToolNode.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ToolNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* harmony import */ var _lib_oauth_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/oauth/config */ \"(app-pages-browser)/./src/lib/oauth/config.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ToolNode(param) {\n    let { data, onUpdate } = param;\n    _s();\n    const [showConfigModal, setShowConfigModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const config = data.config;\n    const toolType = config === null || config === void 0 ? void 0 : config.toolType;\n    const toolIcon = toolType ? _lib_oauth_config__WEBPACK_IMPORTED_MODULE_3__.TOOL_ICONS[toolType] : '🔧';\n    const toolName = toolType ? _lib_oauth_config__WEBPACK_IMPORTED_MODULE_3__.TOOL_DISPLAY_NAMES[toolType] : 'External Tool';\n    const connectionStatus = (config === null || config === void 0 ? void 0 : config.connectionStatus) || 'disconnected';\n    const isAuthenticated = (config === null || config === void 0 ? void 0 : config.isAuthenticated) || false;\n    const getStatusColor = ()=>{\n        switch(connectionStatus){\n            case 'connected':\n                return 'text-green-400';\n            case 'error':\n                return 'text-red-400';\n            default:\n                return 'text-yellow-400';\n        }\n    };\n    const getStatusText = ()=>{\n        switch(connectionStatus){\n            case 'connected':\n                return 'Connected';\n            case 'error':\n                return 'Error';\n            default:\n                return 'Not Connected';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        color: \"#06b6d4\",\n        hasInput: true,\n        hasOutput: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: toolType ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: toolIcon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-white\",\n                                        children: toolName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                        lineNumber: 56,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                lineNumber: 54,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs \".concat(getStatusColor()),\n                                children: \"●\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                lineNumber: 60,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 53,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs \".concat(getStatusColor()),\n                        children: getStatusText()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 13\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.timeout) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: [\n                            \"Timeout: \",\n                            config.timeout,\n                            \"s\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 72,\n                        columnNumber: 15\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-cyan-300 bg-cyan-900/20 px-2 py-1 rounded\",\n                        children: \"✓ Tool configured\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                lineNumber: 52,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-300\",\n                        children: \"External Tool Integration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: \"Connect to external services like Google Drive, databases, APIs, or browser automation.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: \"⚠️ Needs configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                lineNumber: 82,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_s(ToolNode, \"+ivVQaXm6FO+XQ5hCuQDxg1mQNg=\");\n_c = ToolNode;\nvar _c;\n$RefreshReg$(_c, \"ToolNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL21hbnVhbC1idWlsZC9ub2Rlcy9Ub29sTm9kZS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBRWlDO0FBQzZFO0FBQzVFO0FBRWtDO0FBVXJELFNBQVNLLFNBQVMsS0FBaUM7UUFBakMsRUFBRUMsSUFBSSxFQUFFQyxRQUFRLEVBQWlCLEdBQWpDOztJQUMvQixNQUFNLENBQUNDLGlCQUFpQkMsbUJBQW1CLEdBQUdULCtDQUFRQSxDQUFDO0lBQ3ZELE1BQU1VLFNBQVNKLEtBQUtJLE1BQU07SUFDMUIsTUFBTUMsV0FBV0QsbUJBQUFBLDZCQUFBQSxPQUFRQyxRQUFRO0lBQ2pDLE1BQU1DLFdBQVdELFdBQVdQLHlEQUFVLENBQUNPLFNBQVMsR0FBRztJQUNuRCxNQUFNRSxXQUFXRixXQUFXUixpRUFBa0IsQ0FBQ1EsU0FBUyxHQUFHO0lBQzNELE1BQU1HLG1CQUFtQkosQ0FBQUEsbUJBQUFBLDZCQUFBQSxPQUFRSSxnQkFBZ0IsS0FBSTtJQUNyRCxNQUFNQyxrQkFBa0JMLENBQUFBLG1CQUFBQSw2QkFBQUEsT0FBUUssZUFBZSxLQUFJO0lBRW5ELE1BQU1DLGlCQUFpQjtRQUNyQixPQUFRRjtZQUNOLEtBQUs7Z0JBQWEsT0FBTztZQUN6QixLQUFLO2dCQUFTLE9BQU87WUFDckI7Z0JBQVMsT0FBTztRQUNsQjtJQUNGO0lBRUEsTUFBTUcsZ0JBQWdCO1FBQ3BCLE9BQVFIO1lBQ04sS0FBSztnQkFBYSxPQUFPO1lBQ3pCLEtBQUs7Z0JBQVMsT0FBTztZQUNyQjtnQkFBUyxPQUFPO1FBQ2xCO0lBQ0Y7SUFFQSxxQkFDRSw4REFBQ1osaURBQVFBO1FBQ1BJLE1BQU1BO1FBQ05ZLE1BQU1qQiwrR0FBcUJBO1FBQzNCa0IsT0FBTTtRQUNOQyxVQUFVO1FBQ1ZDLFdBQVc7a0JBRVgsNEVBQUNDO1lBQUlDLFdBQVU7c0JBQ1paLHlCQUNDLDhEQUFDVztnQkFBSUMsV0FBVTs7a0NBQ2IsOERBQUNEO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ0Q7Z0NBQUlDLFdBQVU7O2tEQUNiLDhEQUFDQzt3Q0FBS0QsV0FBVTtrREFBV1g7Ozs7OztrREFDM0IsOERBQUNZO3dDQUFLRCxXQUFVO2tEQUNiVjs7Ozs7Ozs7Ozs7OzBDQUdMLDhEQUFDUztnQ0FBSUMsV0FBVyxXQUE0QixPQUFqQlA7MENBQW9COzs7Ozs7Ozs7Ozs7a0NBS2pELDhEQUFDTTt3QkFBSUMsV0FBVyxXQUE0QixPQUFqQlA7a0NBQ3hCQzs7Ozs7O29CQUtGUCxDQUFBQSxtQkFBQUEsNkJBQUFBLE9BQVFlLE9BQU8sbUJBQ2QsOERBQUNIO3dCQUFJQyxXQUFVOzs0QkFBd0I7NEJBQzNCYixPQUFPZSxPQUFPOzRCQUFDOzs7Ozs7O2tDQUk3Qiw4REFBQ0g7d0JBQUlDLFdBQVU7a0NBQXlEOzs7Ozs7Ozs7OztxQ0FLMUUsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQXdCOzs7Ozs7a0NBR3ZDLDhEQUFDRDt3QkFBSUMsV0FBVTtrQ0FBd0I7Ozs7OztrQ0FHdkMsOERBQUNEO3dCQUFJQyxXQUFVO2tDQUE2RDs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQVF4RjtHQWhGd0JsQjtLQUFBQSIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXGNvbXBvbmVudHNcXG1hbnVhbC1idWlsZFxcbm9kZXNcXFRvb2xOb2RlLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgV3JlbmNoU2NyZXdkcml2ZXJJY29uLCBDaGVja0NpcmNsZUljb24sIEV4Y2xhbWF0aW9uVHJpYW5nbGVJY29uIH0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJztcbmltcG9ydCBCYXNlTm9kZSBmcm9tICcuL0Jhc2VOb2RlJztcbmltcG9ydCB7IFdvcmtmbG93Tm9kZSwgVG9vbE5vZGVEYXRhIH0gZnJvbSAnQC90eXBlcy9tYW51YWxCdWlsZCc7XG5pbXBvcnQgeyBUT09MX0RJU1BMQVlfTkFNRVMsIFRPT0xfSUNPTlMgfSBmcm9tICdAL2xpYi9vYXV0aC9jb25maWcnO1xuaW1wb3J0IFRvb2xDb25maWdNb2RhbCBmcm9tICcuLi9Ub29sQ29uZmlnTW9kYWwnO1xuXG5cblxuaW50ZXJmYWNlIFRvb2xOb2RlUHJvcHMge1xuICBkYXRhOiBXb3JrZmxvd05vZGVbJ2RhdGEnXTtcbiAgb25VcGRhdGU/OiAobm9kZUlkOiBzdHJpbmcsIG5ld0RhdGE6IGFueSkgPT4gdm9pZDtcbn1cblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gVG9vbE5vZGUoeyBkYXRhLCBvblVwZGF0ZSB9OiBUb29sTm9kZVByb3BzKSB7XG4gIGNvbnN0IFtzaG93Q29uZmlnTW9kYWwsIHNldFNob3dDb25maWdNb2RhbF0gPSB1c2VTdGF0ZShmYWxzZSk7XG4gIGNvbnN0IGNvbmZpZyA9IGRhdGEuY29uZmlnIGFzIFRvb2xOb2RlRGF0YVsnY29uZmlnJ107XG4gIGNvbnN0IHRvb2xUeXBlID0gY29uZmlnPy50b29sVHlwZTtcbiAgY29uc3QgdG9vbEljb24gPSB0b29sVHlwZSA/IFRPT0xfSUNPTlNbdG9vbFR5cGVdIDogJ/CflKcnO1xuICBjb25zdCB0b29sTmFtZSA9IHRvb2xUeXBlID8gVE9PTF9ESVNQTEFZX05BTUVTW3Rvb2xUeXBlXSA6ICdFeHRlcm5hbCBUb29sJztcbiAgY29uc3QgY29ubmVjdGlvblN0YXR1cyA9IGNvbmZpZz8uY29ubmVjdGlvblN0YXR1cyB8fCAnZGlzY29ubmVjdGVkJztcbiAgY29uc3QgaXNBdXRoZW50aWNhdGVkID0gY29uZmlnPy5pc0F1dGhlbnRpY2F0ZWQgfHwgZmFsc2U7XG5cbiAgY29uc3QgZ2V0U3RhdHVzQ29sb3IgPSAoKSA9PiB7XG4gICAgc3dpdGNoIChjb25uZWN0aW9uU3RhdHVzKSB7XG4gICAgICBjYXNlICdjb25uZWN0ZWQnOiByZXR1cm4gJ3RleHQtZ3JlZW4tNDAwJztcbiAgICAgIGNhc2UgJ2Vycm9yJzogcmV0dXJuICd0ZXh0LXJlZC00MDAnO1xuICAgICAgZGVmYXVsdDogcmV0dXJuICd0ZXh0LXllbGxvdy00MDAnO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBnZXRTdGF0dXNUZXh0ID0gKCkgPT4ge1xuICAgIHN3aXRjaCAoY29ubmVjdGlvblN0YXR1cykge1xuICAgICAgY2FzZSAnY29ubmVjdGVkJzogcmV0dXJuICdDb25uZWN0ZWQnO1xuICAgICAgY2FzZSAnZXJyb3InOiByZXR1cm4gJ0Vycm9yJztcbiAgICAgIGRlZmF1bHQ6IHJldHVybiAnTm90IENvbm5lY3RlZCc7XG4gICAgfVxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPEJhc2VOb2RlXG4gICAgICBkYXRhPXtkYXRhfVxuICAgICAgaWNvbj17V3JlbmNoU2NyZXdkcml2ZXJJY29ufVxuICAgICAgY29sb3I9XCIjMDZiNmQ0XCJcbiAgICAgIGhhc0lucHV0PXt0cnVlfVxuICAgICAgaGFzT3V0cHV0PXt0cnVlfVxuICAgID5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwic3BhY2UteS0zXCI+XG4gICAgICAgIHt0b29sVHlwZSA/IChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMlwiPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWJldHdlZW5cIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtbGdcIj57dG9vbEljb259PC9zcGFuPlxuICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQtc20gZm9udC1tZWRpdW0gdGV4dC13aGl0ZVwiPlxuICAgICAgICAgICAgICAgICAge3Rvb2xOYW1lfVxuICAgICAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdGV4dC14cyAke2dldFN0YXR1c0NvbG9yKCl9YH0+XG4gICAgICAgICAgICAgICAg4pePXG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgdGV4dC14cyAke2dldFN0YXR1c0NvbG9yKCl9YH0+XG4gICAgICAgICAgICAgIHtnZXRTdGF0dXNUZXh0KCl9XG4gICAgICAgICAgICA8L2Rpdj5cblxuXG5cbiAgICAgICAgICAgIHtjb25maWc/LnRpbWVvdXQgJiYgKFxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQteHMgdGV4dC1ncmF5LTQwMFwiPlxuICAgICAgICAgICAgICAgIFRpbWVvdXQ6IHtjb25maWcudGltZW91dH1zXG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIFxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtY3lhbi0zMDAgYmctY3lhbi05MDAvMjAgcHgtMiBweS0xIHJvdW5kZWRcIj5cbiAgICAgICAgICAgICAg4pyTIFRvb2wgY29uZmlndXJlZFxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICkgOiAoXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGFjZS15LTJcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1zbSB0ZXh0LWdyYXktMzAwXCI+XG4gICAgICAgICAgICAgIEV4dGVybmFsIFRvb2wgSW50ZWdyYXRpb25cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS00MDBcIj5cbiAgICAgICAgICAgICAgQ29ubmVjdCB0byBleHRlcm5hbCBzZXJ2aWNlcyBsaWtlIEdvb2dsZSBEcml2ZSwgZGF0YWJhc2VzLCBBUElzLCBvciBicm93c2VyIGF1dG9tYXRpb24uXG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LXllbGxvdy0zMDAgYmcteWVsbG93LTkwMC8yMCBweC0yIHB5LTEgcm91bmRlZFwiPlxuICAgICAgICAgICAgICDimqDvuI8gTmVlZHMgY29uZmlndXJhdGlvblxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICA8L2Rpdj5cbiAgICA8L0Jhc2VOb2RlPlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiV3JlbmNoU2NyZXdkcml2ZXJJY29uIiwiQmFzZU5vZGUiLCJUT09MX0RJU1BMQVlfTkFNRVMiLCJUT09MX0lDT05TIiwiVG9vbE5vZGUiLCJkYXRhIiwib25VcGRhdGUiLCJzaG93Q29uZmlnTW9kYWwiLCJzZXRTaG93Q29uZmlnTW9kYWwiLCJjb25maWciLCJ0b29sVHlwZSIsInRvb2xJY29uIiwidG9vbE5hbWUiLCJjb25uZWN0aW9uU3RhdHVzIiwiaXNBdXRoZW50aWNhdGVkIiwiZ2V0U3RhdHVzQ29sb3IiLCJnZXRTdGF0dXNUZXh0IiwiaWNvbiIsImNvbG9yIiwiaGFzSW5wdXQiLCJoYXNPdXRwdXQiLCJkaXYiLCJjbGFzc05hbWUiLCJzcGFuIiwidGltZW91dCJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/ToolNode.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/oauth/config.ts":
/*!*********************************!*\
  !*** ./src/lib/oauth/config.ts ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TOOL_DESCRIPTIONS: () => (/* binding */ TOOL_DESCRIPTIONS),\n/* harmony export */   TOOL_DISPLAY_NAMES: () => (/* binding */ TOOL_DISPLAY_NAMES),\n/* harmony export */   TOOL_ICONS: () => (/* binding */ TOOL_ICONS),\n/* harmony export */   generateAuthUrl: () => (/* binding */ generateAuthUrl),\n/* harmony export */   getOAuthConfigForTool: () => (/* binding */ getOAuthConfigForTool),\n/* harmony export */   getToolOAuthConfigs: () => (/* binding */ getToolOAuthConfigs),\n/* harmony export */   validateOAuthConfig: () => (/* binding */ validateOAuthConfig)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/process/browser.js\");\n// OAuth Configuration for Tool Integrations\n// This file contains OAuth configurations for all supported tools\n// Get the base URL for redirects\nconst getBaseUrl = ()=>{\n    if (true) {\n        return window.location.origin;\n    }\n    // Server-side detection\n    if (false) {}\n    return process.env.NEXTAUTH_URL || 'http://localhost:3000';\n};\n// Google OAuth configuration for tools\nconst getGoogleToolsConfig = ()=>({\n        clientId: process.env.GOOGLE_TOOLS_OAUTH_CLIENT_ID || process.env.GOOGLE_CLIENT_ID || '',\n        clientSecret: process.env.GOOGLE_TOOLS_OAUTH_CLIENT_SECRET || process.env.GOOGLE_CLIENT_SECRET || '',\n        authorizationUrl: 'https://accounts.google.com/o/oauth2/v2/auth',\n        tokenUrl: 'https://oauth2.googleapis.com/token',\n        scopes: [\n            'https://www.googleapis.com/auth/drive',\n            'https://www.googleapis.com/auth/documents',\n            'https://www.googleapis.com/auth/spreadsheets',\n            'https://www.googleapis.com/auth/gmail.modify',\n            'https://www.googleapis.com/auth/calendar',\n            'https://www.googleapis.com/auth/youtube'\n        ],\n        redirectUri: process.env.GOOGLE_TOOLS_OAUTH_REDIRECT_URI || \"\".concat(getBaseUrl(), \"/api/auth/tools/google/callback\"),\n        additionalParams: {\n            access_type: 'offline',\n            prompt: 'consent',\n            include_granted_scopes: 'true'\n        }\n    });\n// Notion OAuth configuration\nconst getNotionConfig = ()=>({\n        clientId: process.env.NOTION_OAUTH_CLIENT_ID || '',\n        clientSecret: process.env.NOTION_OAUTH_CLIENT_SECRET || '',\n        authorizationUrl: 'https://api.notion.com/v1/oauth/authorize',\n        tokenUrl: 'https://api.notion.com/v1/oauth/token',\n        scopes: [],\n        redirectUri: process.env.NOTION_OAUTH_REDIRECT_URI || \"\".concat(getBaseUrl(), \"/api/auth/tools/notion/callback\"),\n        additionalParams: {\n            owner: 'user',\n            response_type: 'code'\n        }\n    });\n// Tool-specific OAuth configurations\nconst getToolOAuthConfigs = ()=>{\n    const googleConfig = getGoogleToolsConfig();\n    return {\n        // Google services all use the same OAuth config with different scopes\n        google_drive: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/drive'\n            ]\n        },\n        google_docs: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/documents'\n            ]\n        },\n        google_sheets: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/spreadsheets'\n            ]\n        },\n        gmail: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/gmail.modify'\n            ]\n        },\n        calendar: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/calendar'\n            ]\n        },\n        youtube: {\n            ...googleConfig,\n            scopes: [\n                'https://www.googleapis.com/auth/youtube'\n            ]\n        },\n        notion: getNotionConfig(),\n        supabase: {\n            clientId: '',\n            clientSecret: '',\n            authorizationUrl: '',\n            tokenUrl: '',\n            scopes: [],\n            redirectUri: '',\n            additionalParams: {}\n        }\n    };\n};\n// Get OAuth config for a specific tool\nconst getOAuthConfigForTool = (toolType)=>{\n    const configs = getToolOAuthConfigs();\n    return configs[toolType] || null;\n};\n// Validate OAuth configuration\nconst validateOAuthConfig = (config)=>{\n    return !!(config.clientId && config.clientSecret && config.authorizationUrl && config.tokenUrl && config.redirectUri);\n};\n// Generate OAuth authorization URL\nconst generateAuthUrl = (toolType, state)=>{\n    const config = getOAuthConfigForTool(toolType);\n    if (!config || !validateOAuthConfig(config)) {\n        return null;\n    }\n    const params = new URLSearchParams({\n        client_id: config.clientId,\n        redirect_uri: config.redirectUri,\n        response_type: 'code',\n        scope: config.scopes.join(' '),\n        state,\n        ...config.additionalParams\n    });\n    return \"\".concat(config.authorizationUrl, \"?\").concat(params.toString());\n};\n// Tool display names\nconst TOOL_DISPLAY_NAMES = {\n    google_drive: 'Google Drive',\n    google_docs: 'Google Docs',\n    google_sheets: 'Google Sheets',\n    gmail: 'Gmail',\n    calendar: 'Google Calendar',\n    youtube: 'YouTube',\n    notion: 'Notion',\n    supabase: 'Supabase'\n};\n// Tool icons/emojis\nconst TOOL_ICONS = {\n    google_drive: '📁',\n    google_docs: '📄',\n    google_sheets: '📊',\n    gmail: '📧',\n    calendar: '📅',\n    youtube: '📺',\n    notion: '📝',\n    supabase: '🗄️'\n};\n// Tool descriptions\nconst TOOL_DESCRIPTIONS = {\n    google_drive: 'Access and manage Google Drive files',\n    google_docs: 'Create and edit Google Documents',\n    google_sheets: 'Work with Google Spreadsheets',\n    gmail: 'Send and manage emails',\n    calendar: 'Manage calendar events and schedules',\n    youtube: 'Access YouTube data and analytics',\n    notion: 'Access Notion databases and pages',\n    supabase: 'Direct database operations'\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/oauth/config.ts\n"));

/***/ })

});