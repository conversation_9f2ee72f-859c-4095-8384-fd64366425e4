"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/ToolNode.tsx":
/*!********************************************************!*\
  !*** ./src/components/manual-build/nodes/ToolNode.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ToolNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction ToolNode(param) {\n    let { data } = param;\n    const config = data.config;\n    const toolType = config === null || config === void 0 ? void 0 : config.toolType;\n    const toolIcon = toolType ? toolIcons[toolType] : '🔧';\n    const toolName = toolType ? toolNames[toolType] : 'External Tool';\n    const connectionStatus = (config === null || config === void 0 ? void 0 : config.connectionStatus) || 'disconnected';\n    const getStatusColor = ()=>{\n        switch(connectionStatus){\n            case 'connected':\n                return 'text-green-400';\n            case 'error':\n                return 'text-red-400';\n            default:\n                return 'text-yellow-400';\n        }\n    };\n    const getStatusText = ()=>{\n        switch(connectionStatus){\n            case 'connected':\n                return 'Connected';\n            case 'error':\n                return 'Error';\n            default:\n                return 'Not Connected';\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"],\n        color: \"#06b6d4\",\n        hasInput: true,\n        hasOutput: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: toolType ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: toolIcon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                        lineNumber: 52,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-white\",\n                                        children: toolName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                lineNumber: 51,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs \".concat(getStatusColor()),\n                                children: \"●\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                lineNumber: 57,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs \".concat(getStatusColor()),\n                        children: getStatusText()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 13\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.timeout) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: [\n                            \"Timeout: \",\n                            config.timeout,\n                            \"s\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 15\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-cyan-300 bg-cyan-900/20 px-2 py-1 rounded\",\n                        children: \"✓ Tool configured\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 74,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                lineNumber: 49,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-300\",\n                        children: \"External Tool Integration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: \"Connect to external services like Google Drive, databases, APIs, or browser automation.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: \"⚠️ Needs configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                lineNumber: 79,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n        lineNumber: 40,\n        columnNumber: 5\n    }, this);\n}\n_c = ToolNode;\nvar _c;\n$RefreshReg$(_c, \"ToolNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/ToolNode.tsx\n"));

/***/ })

});