// Workflow Tool Executor
// This handles execution of tools within Manual Build workflows

import { ToolNodeData } from '@/types/manualBuild';
import {
  authenticatedGet,
  authenticatedPost,
  authenticatedPut,
  authenticatedPatch,
  validateToolConnection
} from '@/lib/oauth/middleware';
import {
  GoogleDriveAP<PERSON>,
  GoogleDocsAPI,
  GoogleSheetsAPI,
  GmailAPI
} from './toolImplementations';
import {
  GoogleCalendarAPI,
  YouTubeAPI,
  NotionAPI
} from './toolImplementations2';

export interface ToolExecutionContext {
  userInput: string;
  previousResults?: any[];
  workflowId: string;
  nodeId: string;
  userId: string;
}

export interface ToolExecutionResult {
  success: boolean;
  data: any;
  toolType: string;
  executionTime: number;
  error?: string;
}

export class ToolExecutor {
  static async executeToolNode(
    nodeConfig: ToolNodeData['config'],
    context: ToolExecutionContext
  ): Promise<ToolExecutionResult> {
    const startTime = Date.now();
    
    try {
      let result;
      
      // Validate tool connection first
      const connectionValidation = await validateToolConnection(context.userId, nodeConfig.toolType);
      if (!connectionValidation.isValid) {
        throw new Error(connectionValidation.error || 'Tool not connected');
      }

      switch (nodeConfig.toolType) {
        case 'google_drive':
          result = await this.executeGoogleDrive(nodeConfig, context);
          break;

        case 'google_docs':
          result = await this.executeGoogleDocs(nodeConfig, context);
          break;

        case 'google_sheets':
          result = await this.executeGoogleSheets(nodeConfig, context);
          break;

        case 'gmail':
          result = await this.executeGmail(nodeConfig, context);
          break;

        case 'calendar':
          result = await this.executeGoogleCalendar(nodeConfig, context);
          break;

        case 'youtube':
          result = await this.executeYouTube(nodeConfig, context);
          break;

        case 'notion':
          result = await this.executeNotion(nodeConfig, context);
          break;

        case 'supabase':
          result = await this.executeSupabase(nodeConfig, context);
          break;

        default:
          throw new Error(`Unknown tool type: ${nodeConfig.toolType}`);
      }

      const executionTime = Date.now() - startTime;
      
      return {
        success: true,
        data: result.data,
        toolType: nodeConfig.toolType,
        executionTime,
      };
      
    } catch (error) {
      const executionTime = Date.now() - startTime;
      
      return {
        success: false,
        data: null,
        toolType: nodeConfig.toolType || 'unknown',
        executionTime,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  // Helper method to validate tool configuration
  static validateToolConfig(nodeConfig: ToolNodeData['config']): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!nodeConfig.toolType) {
      errors.push('Tool type is required');
    }

    switch (nodeConfig.toolType) {
      case 'web_browsing':
        // Web browsing doesn't require authentication
        break;

      case 'google_drive':
      case 'google_docs':
      case 'google_sheets':
      case 'zapier':
      case 'notion':
      case 'calendar':
      case 'gmail':
      case 'youtube':
      case 'supabase':
        if (!nodeConfig.isAuthenticated) {
          errors.push(`${nodeConfig.toolType} requires authentication`);
        }
        break;

      default:
        errors.push(`Unknown tool type: ${nodeConfig.toolType}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Get human-readable description of what the tool will do
  static getToolDescription(nodeConfig: ToolNodeData['config']): string {
    switch (nodeConfig.toolType) {
      case 'google_drive':
        return 'Access and manage Google Drive files';
      case 'google_docs':
        return 'Create and edit Google Documents';
      case 'google_sheets':
        return 'Work with Google Spreadsheets';
      case 'zapier':
        return 'Connect with 5000+ apps via Zapier';
      case 'notion':
        return 'Access Notion databases and pages';
      case 'calendar':
        return 'Manage calendar events and schedules';
      case 'gmail':
        return 'Send and manage emails';
      case 'youtube':
        return 'Access YouTube data and analytics';
      case 'supabase':
        return 'Direct database operations';
      default:
        return 'External tool integration';
    }
  }

  // Get example usage for the tool
  static getToolExampleUsage(nodeConfig: ToolNodeData['config']): string {
    switch (nodeConfig.toolType) {
      case 'web_browsing':
        return 'Example: "search for latest AI news" or "find information about climate change"';
      case 'google_drive':
        return 'Example: "list files in folder" or "upload document"';
      case 'google_docs':
        return 'Example: "create new document" or "edit document content"';
      case 'google_sheets':
        return 'Example: "add row to spreadsheet" or "calculate sum"';
      case 'zapier':
        return 'Example: "trigger workflow" or "send data to app"';
      case 'notion':
        return 'Example: "create page" or "query database"';
      case 'calendar':
        return 'Example: "schedule meeting" or "check availability"';
      case 'gmail':
        return 'Example: "send email" or "check inbox"';
      case 'youtube':
        return 'Example: "get video stats" or "upload video"';
      case 'supabase':
        return 'Example: "query table" or "insert record"';
      default:
        return 'Tool-specific input required';
    }
  }

  // Check if tool is ready to use
  static isToolReady(nodeConfig: ToolNodeData['config']): boolean {
    if (nodeConfig.toolType === 'web_browsing') {
      return true; // Web browsing is always ready
    }
    return nodeConfig.isAuthenticated || false;
  }

  // Get tool status
  static getToolStatus(nodeConfig: ToolNodeData['config']): {
    status: 'ready' | 'needs_auth' | 'error' | 'not_implemented';
    message: string;
  } {
    switch (nodeConfig.toolType) {
      case 'web_browsing':
        return {
          status: 'ready',
          message: 'Web browsing is ready to use'
        };
      case 'google_drive':
      case 'google_docs':
      case 'google_sheets':
      case 'zapier':
      case 'notion':
      case 'calendar':
      case 'gmail':
      case 'youtube':
      case 'supabase':
        return {
          status: 'not_implemented',
          message: `${nodeConfig.toolType} integration coming soon`
        };
      default:
        return {
          status: 'error',
          message: 'Unknown tool type'
        };
    }
  }

  /**
   * Execute Google Drive tool
   */
  private static async executeGoogleDrive(
    nodeConfig: ToolNodeData['config'],
    context: ToolExecutionContext
  ): Promise<any> {
    console.log('📁 Executing Google Drive tool');

    const { userInput, userId } = context;
    const timeout = nodeConfig.timeout || 30;

    try {
      // Parse user input to determine action
      const action = this.parseGoogleDriveAction(userInput);

      switch (action.type) {
        case 'list_files':
          return await GoogleDriveAPI.listFiles(userId, action.params, timeout);
        case 'get_file':
          return await GoogleDriveAPI.getFile(userId, action.params, timeout);
        case 'create_file':
          return await GoogleDriveAPI.createFile(userId, action.params, timeout);
        case 'search_files':
          return await GoogleDriveAPI.searchFiles(userId, action.params, timeout);
        default:
          throw new Error(`Unsupported Google Drive action: ${action.type}`);
      }
    } catch (error) {
      console.error('Google Drive tool error:', error);
      throw new Error(`Google Drive operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute Google Docs tool
   */
  private static async executeGoogleDocs(
    nodeConfig: ToolNodeData['config'],
    context: ToolExecutionContext
  ): Promise<any> {
    console.log('📄 Executing Google Docs tool');

    const { userInput, userId } = context;
    const timeout = nodeConfig.timeout || 30;

    try {
      const action = this.parseGoogleDocsAction(userInput);

      switch (action.type) {
        case 'create_document':
          return await GoogleDocsAPI.createDocument(userId, action.params, timeout);
        case 'get_document':
          return await GoogleDocsAPI.getDocument(userId, action.params, timeout);
        case 'update_document':
          return await GoogleDocsAPI.updateDocument(userId, action.params, timeout);
        default:
          throw new Error(`Unsupported Google Docs action: ${action.type}`);
      }
    } catch (error) {
      console.error('Google Docs tool error:', error);
      throw new Error(`Google Docs operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute Google Sheets tool
   */
  private static async executeGoogleSheets(
    nodeConfig: ToolNodeData['config'],
    context: ToolExecutionContext
  ): Promise<any> {
    console.log('📊 Executing Google Sheets tool');

    const { userInput, userId } = context;
    const timeout = nodeConfig.timeout || 30;

    try {
      const action = this.parseGoogleSheetsAction(userInput);

      switch (action.type) {
        case 'create_spreadsheet':
          return await this.createGoogleSheet(userId, action.params, timeout);
        case 'get_spreadsheet':
          return await this.getGoogleSheet(userId, action.params, timeout);
        case 'update_cells':
          return await this.updateGoogleSheetCells(userId, action.params, timeout);
        case 'read_range':
          return await this.readGoogleSheetRange(userId, action.params, timeout);
        case 'append_row':
          return await this.appendGoogleSheetRow(userId, action.params, timeout);
        default:
          throw new Error(`Unsupported Google Sheets action: ${action.type}`);
      }
    } catch (error) {
      console.error('Google Sheets tool error:', error);
      throw new Error(`Google Sheets operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute Gmail tool
   */
  private static async executeGmail(
    nodeConfig: ToolNodeData['config'],
    context: ToolExecutionContext
  ): Promise<any> {
    console.log('📧 Executing Gmail tool');

    const { userInput, userId } = context;
    const timeout = nodeConfig.timeout || 30;

    try {
      const action = this.parseGmailAction(userInput);

      switch (action.type) {
        case 'send_email':
          return await this.sendGmailEmail(userId, action.params, timeout);
        case 'list_emails':
          return await this.listGmailEmails(userId, action.params, timeout);
        case 'get_email':
          return await this.getGmailEmail(userId, action.params, timeout);
        case 'search_emails':
          return await this.searchGmailEmails(userId, action.params, timeout);
        default:
          throw new Error(`Unsupported Gmail action: ${action.type}`);
      }
    } catch (error) {
      console.error('Gmail tool error:', error);
      throw new Error(`Gmail operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute Google Calendar tool
   */
  private static async executeGoogleCalendar(
    nodeConfig: ToolNodeData['config'],
    context: ToolExecutionContext
  ): Promise<any> {
    console.log('📅 Executing Google Calendar tool');

    const { userInput, userId } = context;
    const timeout = nodeConfig.timeout || 30;

    try {
      const action = this.parseGoogleCalendarAction(userInput);

      switch (action.type) {
        case 'create_event':
          return await this.createGoogleCalendarEvent(userId, action.params, timeout);
        case 'list_events':
          return await this.listGoogleCalendarEvents(userId, action.params, timeout);
        case 'get_event':
          return await this.getGoogleCalendarEvent(userId, action.params, timeout);
        case 'update_event':
          return await this.updateGoogleCalendarEvent(userId, action.params, timeout);
        case 'delete_event':
          return await this.deleteGoogleCalendarEvent(userId, action.params, timeout);
        default:
          throw new Error(`Unsupported Google Calendar action: ${action.type}`);
      }
    } catch (error) {
      console.error('Google Calendar tool error:', error);
      throw new Error(`Google Calendar operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute YouTube tool
   */
  private static async executeYouTube(
    nodeConfig: ToolNodeData['config'],
    context: ToolExecutionContext
  ): Promise<any> {
    console.log('📺 Executing YouTube tool');

    const { userInput, userId } = context;
    const timeout = nodeConfig.timeout || 30;

    try {
      const action = this.parseYouTubeAction(userInput);

      switch (action.type) {
        case 'search_videos':
          return await this.searchYouTubeVideos(userId, action.params, timeout);
        case 'get_video':
          return await this.getYouTubeVideo(userId, action.params, timeout);
        case 'get_channel':
          return await this.getYouTubeChannel(userId, action.params, timeout);
        case 'get_analytics':
          return await this.getYouTubeAnalytics(userId, action.params, timeout);
        default:
          throw new Error(`Unsupported YouTube action: ${action.type}`);
      }
    } catch (error) {
      console.error('YouTube tool error:', error);
      throw new Error(`YouTube operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute Notion tool
   */
  private static async executeNotion(
    nodeConfig: ToolNodeData['config'],
    context: ToolExecutionContext
  ): Promise<any> {
    console.log('📝 Executing Notion tool');

    const { userInput, userId } = context;
    const timeout = nodeConfig.timeout || 30;

    try {
      const action = this.parseNotionAction(userInput);

      switch (action.type) {
        case 'create_page':
          return await this.createNotionPage(userId, action.params, timeout);
        case 'get_page':
          return await this.getNotionPage(userId, action.params, timeout);
        case 'update_page':
          return await this.updateNotionPage(userId, action.params, timeout);
        case 'query_database':
          return await this.queryNotionDatabase(userId, action.params, timeout);
        case 'create_database_entry':
          return await this.createNotionDatabaseEntry(userId, action.params, timeout);
        default:
          throw new Error(`Unsupported Notion action: ${action.type}`);
      }
    } catch (error) {
      console.error('Notion tool error:', error);
      throw new Error(`Notion operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Execute Supabase tool
   */
  private static async executeSupabase(
    nodeConfig: ToolNodeData['config'],
    context: ToolExecutionContext
  ): Promise<any> {
    console.log('🗄️ Executing Supabase tool');

    const { userInput, userId } = context;
    const timeout = nodeConfig.timeout || 30;

    try {
      const action = this.parseSupabaseAction(userInput);

      switch (action.type) {
        case 'query_table':
          return await this.querySupabaseTable(userId, action.params, timeout);
        case 'insert_record':
          return await this.insertSupabaseRecord(userId, action.params, timeout);
        case 'update_record':
          return await this.updateSupabaseRecord(userId, action.params, timeout);
        case 'delete_record':
          return await this.deleteSupabaseRecord(userId, action.params, timeout);
        default:
          throw new Error(`Unsupported Supabase action: ${action.type}`);
      }
    } catch (error) {
      console.error('Supabase tool error:', error);
      throw new Error(`Supabase operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}
