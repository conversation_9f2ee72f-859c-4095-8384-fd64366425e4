"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/manual-build/[workflowId]/page",{

/***/ "(app-pages-browser)/./src/components/manual-build/nodes/ToolNode.tsx":
/*!********************************************************!*\
  !*** ./src/components/manual-build/nodes/ToolNode.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ToolNode)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ExclamationTriangleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CheckCircleIcon,ExclamationTriangleIcon,WrenchScrewdriverIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/WrenchScrewdriverIcon.js\");\n/* harmony import */ var _BaseNode__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./BaseNode */ \"(app-pages-browser)/./src/components/manual-build/nodes/BaseNode.tsx\");\n/* harmony import */ var _lib_oauth_config__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/oauth/config */ \"(app-pages-browser)/./src/lib/oauth/config.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction ToolNode(param) {\n    let { data, onUpdate } = param;\n    _s();\n    const [showConfigModal, setShowConfigModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const config = data.config;\n    const toolType = config === null || config === void 0 ? void 0 : config.toolType;\n    const toolIcon = toolType ? _lib_oauth_config__WEBPACK_IMPORTED_MODULE_3__.TOOL_ICONS[toolType] : '🔧';\n    const toolName = toolType ? _lib_oauth_config__WEBPACK_IMPORTED_MODULE_3__.TOOL_DISPLAY_NAMES[toolType] : 'External Tool';\n    const connectionStatus = (config === null || config === void 0 ? void 0 : config.connectionStatus) || 'disconnected';\n    const isAuthenticated = (config === null || config === void 0 ? void 0 : config.isAuthenticated) || false;\n    const getStatusColor = ()=>{\n        if (isAuthenticated && connectionStatus === 'connected') {\n            return 'text-green-400';\n        }\n        switch(connectionStatus){\n            case 'expired':\n            case 'revoked':\n            case 'error':\n                return 'text-red-400';\n            default:\n                return 'text-yellow-400';\n        }\n    };\n    const getStatusText = ()=>{\n        if (isAuthenticated && connectionStatus === 'connected') {\n            return 'Connected';\n        }\n        switch(connectionStatus){\n            case 'expired':\n                return 'Expired';\n            case 'revoked':\n                return 'Revoked';\n            case 'error':\n                return 'Error';\n            default:\n                return 'Not Connected';\n        }\n    };\n    const getStatusIcon = ()=>{\n        if (isAuthenticated && connectionStatus === 'connected') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"h-4 w-4 text-green-400\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                lineNumber: 54,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            className: \"h-4 w-4 text-yellow-400\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n            lineNumber: 56,\n            columnNumber: 12\n        }, this);\n    };\n    const handleConfigUpdate = (newConfig)=>{\n        if (onUpdate && data.id) {\n            onUpdate(data.id, {\n                ...data,\n                config: newConfig\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_BaseNode__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        data: data,\n        icon: _barrel_optimize_names_CheckCircleIcon_ExclamationTriangleIcon_WrenchScrewdriverIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        color: \"#06b6d4\",\n        hasInput: true,\n        hasOutput: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-3\",\n            children: toolType ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-lg\",\n                                        children: toolIcon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                        lineNumber: 81,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm font-medium text-white\",\n                                        children: toolName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                        lineNumber: 82,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-xs \".concat(getStatusColor()),\n                                children: \"●\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs \".concat(getStatusColor()),\n                        children: getStatusText()\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 13\n                    }, this),\n                    (config === null || config === void 0 ? void 0 : config.timeout) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: [\n                            \"Timeout: \",\n                            config.timeout,\n                            \"s\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 15\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-cyan-300 bg-cyan-900/20 px-2 py-1 rounded\",\n                        children: \"✓ Tool configured\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                lineNumber: 78,\n                columnNumber: 11\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-sm text-gray-300\",\n                        children: \"External Tool Integration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-gray-400\",\n                        children: \"Connect to external services like Google Drive, databases, APIs, or browser automation.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-xs text-yellow-300 bg-yellow-900/20 px-2 py-1 rounded\",\n                        children: \"⚠️ Needs configuration\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n                lineNumber: 108,\n                columnNumber: 11\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n            lineNumber: 76,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\manual-build\\\\nodes\\\\ToolNode.tsx\",\n        lineNumber: 69,\n        columnNumber: 5\n    }, this);\n}\n_s(ToolNode, \"+ivVQaXm6FO+XQ5hCuQDxg1mQNg=\");\n_c = ToolNode;\nvar _c;\n$RefreshReg$(_c, \"ToolNode\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/manual-build/nodes/ToolNode.tsx\n"));

/***/ })

});