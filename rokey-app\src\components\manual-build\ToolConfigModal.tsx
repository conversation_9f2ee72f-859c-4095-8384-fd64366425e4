'use client';

import { useState, useEffect } from 'react';
import { Dialog, Transition } from '@headlessui/react';
import { Fragment } from 'react';
import { XMarkIcon, LinkIcon, CheckCircleIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline';
import { TOOL_DISPLAY_NAMES, TOOL_ICONS, TOOL_DESCRIPTIONS } from '@/lib/oauth/config';
import { useSupabase } from '@/lib/supabase/client';

interface ToolConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  nodeId: string;
  currentConfig: any;
  onConfigUpdate: (config: any) => void;
}

interface ToolStatus {
  tool_type: string;
  display_name: string;
  icon: string;
  description: string;
  is_connected: boolean;
  connection_status: string;
  provider_user_email?: string;
  provider_user_name?: string;
}

export default function ToolConfigModal({
  isOpen,
  onClose,
  nodeId,
  currentConfig,
  onConfigUpdate
}: ToolConfigModalProps) {
  const [selectedTool, setSelectedTool] = useState(currentConfig?.toolType || '');
  const [toolStatus, setToolStatus] = useState<ToolStatus | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [timeout, setTimeout] = useState(currentConfig?.timeout || 30);
  const supabase = useSupabase();

  // Available tools (excluding zapier as per user request)
  const availableTools = Object.keys(TOOL_DISPLAY_NAMES).filter(tool => tool !== 'zapier');

  // Fetch tool connection status
  const fetchToolStatus = async (toolType: string) => {
    if (!toolType) return;
    
    try {
      const response = await fetch(`/api/auth/tools/status?tool=${toolType}`);
      if (response.ok) {
        const status = await response.json();
        setToolStatus(status);
      }
    } catch (error) {
      console.error('Error fetching tool status:', error);
    }
  };

  // Handle tool selection change
  const handleToolChange = (toolType: string) => {
    setSelectedTool(toolType);
    setError(null);
    fetchToolStatus(toolType);
  };

  // Handle account linking
  const handleLinkAccount = async () => {
    if (!selectedTool) return;
    
    setIsConnecting(true);
    setError(null);
    
    try {
      // Determine the OAuth provider based on tool type
      let provider = 'google';
      if (selectedTool === 'notion') {
        provider = 'notion';
      }
      
      // Get authorization URL
      const response = await fetch(`/api/auth/tools/${provider}/authorize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          toolType: selectedTool,
          returnUrl: window.location.pathname
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to initiate OAuth flow');
      }
      
      const { authUrl } = await response.json();
      
      // Open OAuth flow in new window
      const popup = window.open(
        authUrl,
        'oauth-popup',
        'width=600,height=700,scrollbars=yes,resizable=yes'
      );
      
      // Listen for OAuth completion
      const checkClosed = setInterval(() => {
        if (popup?.closed) {
          clearInterval(checkClosed);
          setIsConnecting(false);
          // Refresh tool status
          setTimeout(() => fetchToolStatus(selectedTool), 1000);
        }
      }, 1000);
      
    } catch (error) {
      console.error('Error linking account:', error);
      setError(error instanceof Error ? error.message : 'Failed to link account');
      setIsConnecting(false);
    }
  };

  // Handle disconnect
  const handleDisconnect = async () => {
    if (!selectedTool) return;
    
    try {
      const response = await fetch(`/api/auth/tools/status?tool=${selectedTool}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        setToolStatus(null);
        fetchToolStatus(selectedTool);
      }
    } catch (error) {
      console.error('Error disconnecting tool:', error);
      setError('Failed to disconnect tool');
    }
  };

  // Save configuration
  const handleSave = () => {
    if (!selectedTool) {
      setError('Please select a tool');
      return;
    }
    
    const newConfig = {
      toolType: selectedTool,
      timeout,
      connectionStatus: toolStatus?.is_connected ? 'connected' : 'disconnected',
      isAuthenticated: toolStatus?.is_connected || false,
      providerUserEmail: toolStatus?.provider_user_email,
      providerUserName: toolStatus?.provider_user_name,
      toolConfig: {}
    };
    
    onConfigUpdate(newConfig);
    onClose();
  };

  // Load tool status on modal open
  useEffect(() => {
    if (isOpen && selectedTool) {
      fetchToolStatus(selectedTool);
    }
  }, [isOpen, selectedTool]);

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-gray-800 p-6 text-left align-middle shadow-xl transition-all border border-gray-700">
                <div className="flex items-center justify-between mb-4">
                  <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-white">
                    Configure Tool
                  </Dialog.Title>
                  <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>

                <div className="space-y-4">
                  {/* Tool Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Select Tool
                    </label>
                    <select
                      value={selectedTool}
                      onChange={(e) => handleToolChange(e.target.value)}
                      className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
                    >
                      <option value="">Choose a tool...</option>
                      {availableTools.map((tool) => (
                        <option key={tool} value={tool}>
                          {TOOL_ICONS[tool]} {TOOL_DISPLAY_NAMES[tool]}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Tool Description */}
                  {selectedTool && (
                    <div className="p-3 bg-gray-700 rounded-md">
                      <p className="text-sm text-gray-300">
                        {TOOL_DESCRIPTIONS[selectedTool]}
                      </p>
                    </div>
                  )}

                  {/* Connection Status */}
                  {selectedTool && toolStatus && (
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 bg-gray-700 rounded-md">
                        <div className="flex items-center gap-2">
                          <span className="text-lg">{toolStatus.icon}</span>
                          <div>
                            <div className="text-sm font-medium text-white">
                              {toolStatus.display_name}
                            </div>
                            {toolStatus.provider_user_email && (
                              <div className="text-xs text-gray-400">
                                {toolStatus.provider_user_email}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {toolStatus.is_connected ? (
                            <CheckCircleIcon className="h-5 w-5 text-green-400" />
                          ) : (
                            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
                          )}
                          <span className={`text-xs ${
                            toolStatus.is_connected ? 'text-green-400' : 'text-yellow-400'
                          }`}>
                            {toolStatus.is_connected ? 'Connected' : 'Not Connected'}
                          </span>
                        </div>
                      </div>

                      {/* Connection Actions */}
                      <div className="flex gap-2">
                        {toolStatus.is_connected ? (
                          <button
                            onClick={handleDisconnect}
                            className="flex-1 px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded-md transition-colors"
                          >
                            Disconnect
                          </button>
                        ) : (
                          <button
                            onClick={handleLinkAccount}
                            disabled={isConnecting}
                            className="flex-1 px-3 py-2 bg-cyan-600 hover:bg-cyan-700 disabled:bg-gray-600 text-white text-sm rounded-md transition-colors flex items-center justify-center gap-2"
                          >
                            <LinkIcon className="h-4 w-4" />
                            {isConnecting ? 'Connecting...' : 'Link Account'}
                          </button>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Timeout Setting */}
                  {selectedTool && (
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Timeout (seconds)
                      </label>
                      <input
                        type="number"
                        min="5"
                        max="300"
                        value={timeout}
                        onChange={(e) => setTimeout(parseInt(e.target.value) || 30)}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
                      />
                    </div>
                  )}

                  {/* Error Display */}
                  {error && (
                    <div className="p-3 bg-red-900/20 border border-red-500/20 rounded-md">
                      <p className="text-sm text-red-400">{error}</p>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 mt-6">
                  <button
                    onClick={onClose}
                    className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSave}
                    disabled={!selectedTool}
                    className="flex-1 px-4 py-2 bg-cyan-600 hover:bg-cyan-700 disabled:bg-gray-600 text-white rounded-md transition-colors"
                  >
                    Save
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
